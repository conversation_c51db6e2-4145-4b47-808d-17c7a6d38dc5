import { TouchEvent, useCallback, useRef } from "react";

export const useTap = (
  callback?: (e: TouchEvent) => void,
  maxMoveDistance: number = 10
) => {
  const isTapping = useRef(false);
  const startPosition = useRef<{ x: number; y: number } | null>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    e.preventDefault();
    // 只处理单点触控
    if (e.touches.length !== 1) {
      isTapping.current = false;
      return;
    }

    isTapping.current = true;
    const touch = e.touches[0];
    if (touch) {
      startPosition.current = { x: touch.clientX, y: touch.clientY };
    }
  }, []);

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      e.preventDefault();
      if (!isTapping.current || !startPosition.current) return;

      // 如果变成多点触控，取消tap事件
      if (e.touches.length !== 1) {
        isTapping.current = false;
        return;
      }

      const touch = e.touches[0];
      if (!touch) return;

      const deltaX = Math.abs(touch.clientX - startPosition.current.x);
      const deltaY = Math.abs(touch.clientY - startPosition.current.y);
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 如果移动距离超过阈值，则取消tap事件
      if (distance > maxMoveDistance) {
        isTapping.current = false;
      }
    },
    [maxMoveDistance]
  );

  const handleTouchEnd = useCallback(
    (e: TouchEvent) => {
      e.preventDefault();
      if (isTapping.current) {
        callback?.(e);
      }
      isTapping.current = false;
      startPosition.current = null;
    },
    [callback]
  );

  const handleTouchCancel = useCallback((e: TouchEvent) => {
    e.preventDefault();
    // touchcancel 时重置状态，不触发回调
    isTapping.current = false;
    startPosition.current = null;
  }, []);

  if (!callback) return {};

  return {
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
    onTouchMove: handleTouchMove,
    onTouchCancel: handleTouchCancel,
  };
};
